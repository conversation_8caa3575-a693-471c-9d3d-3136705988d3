"use client";

import React, { ReactNode } from "react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertTriangle } from "lucide-react";

interface PermissionGuardProps {
  permission: string;
  fallback?: ReactNode;
  children: ReactNode;
  requireAuth?: boolean;
  redirectTo?: string;
}

/**
 * @deprecated PermissionGuard is deprecated. Use role-based guards instead:
 * - RoleGuard for specific roles
 * - AdminGuard for admin-only content
 * - ProviderGuard for catering provider content
 * - ProviderOwnerGuard for provider owner content
 * - ProviderStaffGuard for provider staff content
 * - MultiRoleGuard for multiple role access
 *
 * Import from: @/components/role-guard
 */
export function PermissionGuard({
  permission,
  fallback,
}: // children is intentionally not destructured as this deprecated component
// only renders fallback or deprecation warning, never the actual children
PermissionGuardProps) {
  console.warn(
    `PermissionGuard is deprecated. Permission "${permission}" should be replaced with role-based guards. ` +
      `See components/role-guard.tsx for alternatives.`
  );

  return (
    fallback || (
      <div className="flex items-center justify-center min-h-[400px] p-4">
        <Alert className="max-w-md border-yellow-200 bg-yellow-50">
          <AlertTriangle className="h-4 w-4 text-yellow-600" />
          <AlertTitle className="text-yellow-800">
            Deprecated Component
          </AlertTitle>
          <AlertDescription className="text-yellow-700">
            <p>
              PermissionGuard is deprecated. This component should be replaced
              with role-based guards:
            </p>
            <ul className="mt-2 list-disc list-inside space-y-1 text-sm">
              <li>AdminGuard for admin-only content</li>
              <li>ProviderGuard for catering provider content</li>
              <li>RoleGuard for specific role checks</li>
            </ul>
            <p className="mt-2 text-sm">Import from: @/components/role-guard</p>
          </AlertDescription>
        </Alert>
      </div>
    )
  );
}
